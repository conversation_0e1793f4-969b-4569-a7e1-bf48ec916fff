import ferrobus
import datetime
import json
import time
import geopandas as gpd
from shapely import wkt
# Create a transit model from GTFS and OSM data
model = ferrobus.create_transit_model(
    osm_path="/Users/<USER>/projects/pinpoint-proj/try-ferrobus/berkshire-latest.osm.pbf", # OSM pbf file, preferably filtered by region and tag
    gtfs_dirs=["/Users/<USER>/Downloads/readingbuses_1747733074"], # feeds, operating in the same region
    date=datetime.date.today() # date to use when filtering GTFS data / None for all dates
)

# Create origin and destination points
origin = ferrobus.create_transit_point(
    lat=51.46,
    lon=-0.*********,
    transit_model=model
)

destination = ferrobus.create_transit_point(
    lat=51.44052243152901,
    lon= -0.9522726712711015,
    transit_model=model
)

# # Find the optimal route at noon (12:00)
# route = ferrobus.find_route(
#     transit_model=model,
#     start_point=origin,
#     end_point=destination,
#     departure_time=12*3600,  # 12:00 noon in seconds since midnight
#     max_transfers=3
# )

# # Print the results
# print(f"Travel time: {route['travel_time_seconds'] / 60:.1f} minutes")


# # Get detailed journey information as GeoJSON
# journey = ferrobus.detailed_journey(
#     transit_model=model,
#     start_point=origin,
#     end_point=destination,
#     departure_time=43200,  # 12:00 noon
#     max_transfers=3,
# )

# # Convert to GeoDataFrame for visualization
# journey_gdf = gpd.GeoDataFrame.from_features(json.loads(journey), crs=4326)

# # Display journey information
# print(f"Journey has {len(journey_gdf)} legs")

# # Visualize the journey
# journey_gdf.explore(
#     column="leg_type",
#     cmap="Set1",
#     legend=True,
#     tiles="CartoDB Dark Matter",
#     legend_kwds={"colorbar": False, "loc": "upper right"},
# )


# Show part of the matrix (first 3×3 submatrix)
from statistics import mean

import numpy as np
import pandas as pd



# Create an isochrone index to speed up calculations
# The bounding box is defined as a WKT polygon string
# bounding_box = "POLYGON((-0.98 51.43, -0.98 51.47, -0.94 51.47, -0.94 51.43, -0.98 51.43))"
# BIGGER
index = ferrobus.create_isochrone_index(
    model, bounding_box, 9
)  # 9 represents the grid precision

# Calculate isochrones for different time thresholds
time_thresholds = [3600, 2400, 1800, 900]  # 30, 40, 50 minutes in seconds
isochrones = []

for threshold in time_thresholds:
    isochrone = ferrobus.calculate_isochrone(
        model,
        start=origin,
        departure_time=43200,  # 12:00 noon
        max_transfers=3,
        cutoff=threshold,
        index=index,
    )
    isochrones.append(isochrone)

# Convert to GeoDataFrame for visualization
isochrone_gdf = gpd.GeoDataFrame(
    {
        "time_threshold": [t / 60 for t in time_thresholds],  # Convert to minutes
        "geometry": [wkt.loads(poly) for poly in isochrones],
    },
    crs=4326,
)

# Visualize the isochrones
isochrone_gdf.explore(
    column="time_threshold",
    cmap="viridis_r",
    legend=True,
    legend_title="Reachable within (minutes)",
).show_in_browser()
